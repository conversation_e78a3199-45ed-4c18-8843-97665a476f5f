import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Image from "next/image";
import { supabase } from "@utils/supabaseClient";
import { Oval } from "svg-loaders-react";
import {
  SidebarProvider,
  SidebarTrigger,
  SidebarInset,
} from "@core/components/ui/sidebar";
import { Separator } from "@core/components/ui/separator";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@core/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@core/components/ui/accordion";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  CarouselApi,
} from "@core/components/ui/carousel";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@core/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectLabel,
  SelectGroup,
} from "@core/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@core/components/ui/tabs";

import { Button } from "@core/components/ui/button";
import { Badge } from "@core/components/ui/badge";

import skillsChart from "/public/skills-chart.png";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@core/components/ui/breadcrumb";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@core/components/ui/popover";

import { cn } from "@core/lib/utils";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@core/components/ui/command";

import {
  Check,
  ChevronsUpDown,
  CircleCheck,
  ChevronRight,
  ChevronLeft,
} from "lucide-react";

import { Label } from "@core/components/ui/label";

import { AppSidebar } from "@core/components/app-sidebar";

import TeamBSkills from "@core/Teams/TeamBSkills";
import TeamBSkillsEx from "@core/Teams/TeamBSkillsEx";
import TeamBSkillsGrw from "@core/Teams/TeamBSkillsGrw";

import TeamTSkills from "@core/Teams/TeamTSkills";

const functions = [{ name: "Security", value: "Security" }];

const teams = [
  { name: "Cyber Security Operations", value: "Cyber Security Operations" },
];

const types = [
  { name: "Behavioural skills", value: "Behavioural" },
  { name: "Technical skills", value: "Technical" },
];

export default function Dashboard({
  behavioural_skills,
  technical_skills,
  tech_data_user,
  role_data,
}) {
  const [loading, setLoading] = useState(true);
  const [session, setSession] = useState(null);
  const [userData, setUserData] = useState(null);
  const [userRole, setUserRole] = useState(null);

  const [selectedFunction, setSelectedFunction] = useState(null);
  const [selectedTeam, setSelectedTeam] = useState(null);
  const [selectedType, setSelectedType] = useState(null);
  const [roleIds, setRoleIds] = useState(null);

  const [selectedTeaData, setSelectedTeamData] = useState(null);

  const [open, setOpen] = useState(null);

  const [userBLevel, setUserBLevel] = useState(null);
  const [userBLevelDisplay, setUserBLevelDisplay] = useState(null);
  const [userRoleDesc, setUserRoleDesc] = useState(null);
  const [filteredTechSkills, setFilteredTechSkills] = useState(null);
  const [showBSkills, setShowBSkills] = useState(false);
  const [showTSkills, setShowTSkills] = useState(false);
  const [showTabView, setShowTabView] = useState(false);
  const [carouselApi, setCarouselApi] = useState(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [totalItemsThink, setTotalItemsThink] = useState(0);
  const [totalItemsExecute, setTotalItemsExecute] = useState(0);
  const [totalItemsGrow, setTotalItemsGrow] = useState(0);
  const [skillsLevel1, setSkillsLevel1] = useState(null);
  const [skillsLevel2, setSkillsLevel2] = useState(null);
  const [skillsLevel3, setSkillsLevel3] = useState(null);
  const [skillsLevel4, setSkillsLevel4] = useState(null);

  const router = useRouter();

  useEffect(() => {
    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      if (!session) {
        // Redirect to index page if no session
        router.push("/");
      }
    });

    // Listen for auth state changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      if (!session) {
        // Redirect to index page if session is lost
        router.push("/");
      }
    });

    return () => subscription.unsubscribe();
  }, [router]);

  // Fetch user profile when session is available
  useEffect(() => {
    if (session) {
      getUserProfile();
    }
  }, [session]);

  useEffect(() => {
    if (!carouselApi) return;

    const updateCarouselState = () => {
      setCurrentIndex(carouselApi.selectedScrollSnap());
      setTotalItems(carouselApi.scrollSnapList().length);
    };

    updateCarouselState();

    carouselApi.on("select", updateCarouselState);

    return () => {
      carouselApi.off("select", updateCarouselState); // Clean up on unmount
    };
  }, [carouselApi]);

  const scrollToIndex = (index) => {
    carouselApi?.scrollTo(index);
  };

  /* --- DEBUG --- */

  //   console.log(behavioural_skills);

  //   console.log("user data");
  //   console.log(userData);

  //   console.log("user role");
  //   console.log(userRole);

  //   console.log("selectedTeam");
  //   console.log(selectedTeam);

  //   console.log("selectedType");
  //   console.log(selectedType);

  //   console.log("selectedRole");
  //   console.log(selectedRole);

  //   console.log("role_data");
  //   console.log(role_data);

  //   console.log("selectedFunction");
  //   console.log(selectedFunction);

  /* --- DEBUG --- */

  async function getUserProfile() {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("No user found");
      }

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserData(data);
      }
    } catch (error) {
      console.log("Error fetching user profile:", error.message);
      // If there's an error fetching profile, we can still show the dashboard
      // but userData will remain null
    } finally {
      setLoading(false);
    }
  }

  // get team roles
  async function getTheSelectedTeam(team) {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("No user found");
      }

      let { data, error, status } = await supabase
        .from("roles")
        .select(
          "id, team, role_name, behavioural_skill_level, role_description"
        )
        .eq("team", team)
        .select("*");

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        console.log("team selected");
        console.log(data);
        setSelectedTeamData(data);

        let role_ids = data.map(({ id, role_name }) => ({
          id,
          role_name,
        }));

        setRoleIds(role_ids);

        // console.log("team ids");
        // console.log(team_ids);
      }
    } catch (error) {
      console.log("Error fetching user profile:", error.message);
      // If there's an error fetching profile, we can still show the dashboard
      // but userData will remain null
    } finally {
      setLoading(false);
    }
  }

  // get team roles
  async function getTeamMappedRoles(team) {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("No user found");
      }

      let { data, error, status } = await supabase
        .from("roles")
        .select(
          "id, team, role_name, behavioural_skill_level, role_description"
        )
        .eq("team", team)
        .select("*");

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        // console.log("team selected");
        // console.log(data);
        setSelectedTeamData(data);

        let team_ids = data.map(({ id }) => id);

        console.log("team ids");
        console.log(team_ids);
      }
    } catch (error) {
      console.log("Error fetching user profile:", error.message);
      // If there's an error fetching profile, we can still show the dashboard
      // but userData will remain null
    } finally {
      setLoading(false);
    }
  }

  const getTeam = (team) => {
    getTheSelectedTeam(team);
  };

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="grid place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      </div>
    );
  }

  // Don't render dashboard if no session (will redirect)
  if (!session) {
    return null;
  }

  return (
    <SidebarProvider>
      <AppSidebar
        userData={userData}
        behavioural_skills={behavioural_skills}
        technical_skills={technical_skills}
        // handleShowSkills={handleShowSkills}
      />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-3">
            <SidebarTrigger />
            <Separator orientation="vertical" className="mr-2 h-4" />

            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="#">Team</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink>
                    {selectedTeam ? selectedTeam : "Select a team"}{" "}
                  </BreadcrumbLink>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        {/* {!selectedRole && (
          <div className="flex m-auto mt-12 text-lg">
            Select a role to view the snapshot
          </div>
        )} */}

        <div className="flex flex-row gap-2 p-2 pt-3">
          <div className="flex m-auto">
            <div className="ml-6 pt-1 text-md text-center pb-1 text-primary">
              Select a tean to view the Team Skills
            </div>
            <div className="ml-6 pt-0">
              <Select
                onValueChange={(funcselected) =>
                  setSelectedFunction(funcselected)
                }
              >
                <SelectTrigger className="w-[250px]">
                  <SelectValue
                    placeholder={selectedFunction || "Select Function"}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Function</SelectLabel>
                    {functions.map((func) => (
                      <SelectItem key={func.name} value={func.name}>
                        {func.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="ml-6 pt-0">
              <Select
                disabled={!selectedFunction}
                onValueChange={(teamselected) => {
                  setSelectedTeam(teamselected);
                  //   filterTheRoles(teamselected);
                }}
              >
                <SelectTrigger className="w-[250px]">
                  <SelectValue placeholder={selectedTeam || "Select Team"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Team</SelectLabel>
                    {teams.map((team) => (
                      <SelectItem key={team.name} value={team.name}>
                        {team.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            <div className="ml-6 pt-0">
              <Select
                disabled={!selectedTeam}
                onValueChange={(typeSelected) => {
                  setSelectedType(typeSelected);
                  getTeam(selectedTeam);
                }}
              >
                <SelectTrigger className="w-[250px]">
                  <SelectValue placeholder={selectedType || "Select type"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Type</SelectLabel>
                    {types.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {selectedType == "Behavioural" && (
          <Tabs defaultValue="think" className="mt-4">
            {/* <Tabs defaultValue="skills" className="w-[800px]"> */}
            <TabsList className="">
              {/* <TabsList className="ml-[312px]"> */}
              <TabsTrigger
                value="think"
                className="bg-[#ca005d] text-white font-extrabold text-md w-[50px] h-[50px] rounded-tr-xl rounded-tl-xl rounded-br-none  rounded-bl-none"
              >
                Think
              </TabsTrigger>

              <TabsTrigger
                value="execute"
                className="bg-[#861889] text-white font-extrabold text-md w-[150px] h-[50px] rounded-tr-xl rounded-tl-xl rounded-br-none  rounded-bl-none"
              >
                Execute
              </TabsTrigger>

              <TabsTrigger
                value="grow"
                className="bg-[#5C2071] text-white font-extrabold text-md w-[150px] h-[50px] rounded-tr-xl rounded-tl-xl rounded-br-none  rounded-bl-none"
              >
                Grow
              </TabsTrigger>
            </TabsList>
            <TabsContent value="think">
              {selectedType == "Behavioural" && (
                <div className="flex flex-1 flex-col gap-0 pl-1 pr-1">
                  <div className="grid grid-cols-[25%_15%_15%_15%_15%_15%]">
                    {/* HELP */}
                    <Card className="min-w-8 bg-[#ca005d] text-white">
                      <CardContent
                        className={"font-extrabold text-lg text-center"}
                      >
                        <div className="flex">
                          <div className="m-auto">Skills</div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card className="min-w-8  bg-[#ca005d] text-primary-foreground">
                      <CardContent
                        className={"font-semibold text-sm text-center p-0"}
                      >
                        Strategic Thinking
                      </CardContent>
                    </Card>
                    <Card className="min-w-8  bg-[#ca005d] text-primary-foreground">
                      <CardContent
                        className={"font-semibold text-sm text-center p-0"}
                      >
                        Purposeful Planning
                      </CardContent>
                    </Card>
                    <Card className="min-w-8  bg-[#ca005d] text-primary-foreground">
                      <CardContent
                        className={"font-semibold text-sm text-center p-0"}
                      >
                        Shaping Solutions
                      </CardContent>
                    </Card>
                    <Card className="min-w-8   bg-[#ca005d] text-primary-foreground">
                      <CardContent
                        className={"font-semibold  text-sm text-center"}
                      >
                        Customer Focus
                      </CardContent>
                    </Card>
                    <Card className="min-w-8   bg-[#ca005d] text-primary-foreground">
                      <CardContent
                        className={"font-semibold  text-sm text-center p-0"}
                      >
                        Agile and Adaptable
                      </CardContent>
                    </Card>

                    {/* Teams B data */}

                    <TeamBSkills
                      filteredBSkills={selectedTeaData && selectedTeaData}
                    />
                  </div>
                </div>
              )}
            </TabsContent>

            {/* Execute tab */}

            <TabsContent value="execute">
              {selectedType == "Behavioural" && (
                <div className="flex flex-1 flex-col gap-0 pl-1 pr-1">
                  <div className="grid grid-cols-[25%_18.7%_18.7%_18.7%_18.7%]">
                    {/* HELP */}
                    <Card className="min-w-8 bg-[#861889] text-white">
                      <CardContent
                        className={"font-extrabold text-lg text-center"}
                      >
                        <div className="flex">
                          <div className="m-auto">
                            {/* <HelpCircleIcon />  */}
                            {/* TEAM */}
                            Skills
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="min-w-8   bg-[#861889] text-primary-foreground">
                      <CardContent
                        className={"font-semibold  text-sm text-center"}
                      >
                        Engage and Influence
                      </CardContent>
                    </Card>

                    <Card className="min-w-8   bg-[#861889] text-primary-foreground">
                      <CardContent
                        className={"font-semibold  text-sm text-center"}
                      >
                        Deliver Results
                      </CardContent>
                    </Card>

                    <Card className="min-w-8   bg-[#861889] text-primary-foreground">
                      <CardContent
                        className={"font-semibold  text-sm text-center"}
                      >
                        Collaborate Openly
                      </CardContent>
                    </Card>

                    <Card className="min-w-8   bg-[#861889] text-primary-foreground">
                      <CardContent
                        className={"font-semibold  text-sm text-center"}
                      >
                        Trust and Integrity
                      </CardContent>
                    </Card>

                    {/* TEAM SKILLS EXECUTE */}

                    <TeamBSkillsEx
                      filteredBSkills={selectedTeaData && selectedTeaData}
                    />
                  </div>
                </div>
              )}
            </TabsContent>

            {/* GROW */}

            <TabsContent value="grow">
              {selectedType == "Behavioural" && (
                <div className="flex flex-1 flex-col gap-0 pl-1 pr-1">
                  <div className="grid grid-cols-[25%_25%_25%_25%]">
                    {/* HELP */}
                    <Card className="min-w-8 bg-[#5c2071] text-white">
                      <CardContent
                        className={"font-extrabold text-lg text-center"}
                      >
                        <div className="flex">
                          <div className="m-auto">
                            {/* <HelpCircleIcon />  */}
                            {/* TEAM */}
                            Skills
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="min-w-8   bg-[#5c2071] text-primary-foreground">
                      <CardContent
                        className={"font-semibold  text-sm text-center"}
                      >
                        Develop Self
                      </CardContent>
                    </Card>

                    <Card className="min-w-8   bg-[#5c2071] text-primary-foreground">
                      <CardContent
                        className={"font-semibold  text-sm text-center"}
                      >
                        Enable Performance
                      </CardContent>
                    </Card>

                    <Card className="min-w-8   bg-[#5c2071] text-primary-foreground">
                      <CardContent
                        className={"font-semibold  text-sm text-center"}
                      >
                        Develop Others
                      </CardContent>
                    </Card>

                    {/* TEAM SKILLS GROW */}

                    <TeamBSkillsGrw
                      filteredBSkills={selectedTeaData && selectedTeaData}
                    />
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        )}
        {selectedType == "Technical" && (
          <div className="flex flex-1 flex-col gap-2 p-2">
            {/* <div className="flex-1 rounded-xl bg-muted/100" /> */}

            <div className="grid grid-cols-[10%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%_7.5%]">
              {/* header */}

              <TeamTSkills
                technical_skills={technical_skills}
                role_ids={roleIds && roleIds}
              />

              {/* Row 2 */}

              <Card className="min-w-8  bg-[#1f144a] text-primary-foreground">
                <CardContent className={"font-bold text-sm text-center"}>
                  <div className="inline-block align-middle">SOC Manager</div>
                </CardContent>
              </Card>

              <Card className="min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  N/A
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight] p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#64C7E9] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Expert
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              {/* Row 3 */}

              <Card className="min-w-8  bg-[#1f144a] text-primary-foreground">
                <CardContent className={"font-bold text-sm text-center"}>
                  <div className="inline-block align-middle">
                    Senior Cyber Operations Analyst
                  </div>
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Knowledge
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  N/A
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tigh p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#64C7E9] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Expert
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  N/A
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tigh p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#64C7E9] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Expert
                </CardContent>
              </Card>

              {/* Row 4 */}

              <Card className="min-w-8  bg-[#1f144a] text-primary-foreground">
                <CardContent className={"font-bold text-sm text-center"}>
                  <div className="inline-block align-middle">
                    Cyber Operations Analyst
                  </div>
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Knowledge
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#A1A2A3]  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  N/A
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tigh p-0">
                  Knowledge
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tigh p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#A1A2A3]  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  N/A
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Knowledge
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tigh p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              {/* Row 5 */}

              <Card className="min-w-8  bg-[#1f144a] text-primary-foreground">
                <CardContent className={"font-bold text-sm text-center"}>
                  <div className="inline-block align-middle">
                    Lead Cyber Security Engineer
                  </div>
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  N/A
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  N/A
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#64C7E9] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Expert
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tigh p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Knowledge
                </CardContent>
              </Card>

              {/* Row 6 */}

              <Card className="min-w-8  bg-[#1f144a] text-primary-foreground">
                <CardContent className={"font-bold text-sm text-center"}>
                  <div className="inline-block align-middle">
                    Cyber Security Engineer
                  </div>
                </CardContent>
              </Card>

              <Card className="min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight  p-0">
                  N/A
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  N/A
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#64C7E9] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Expert
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Knowledge
                </CardContent>
              </Card>

              {/* Row 7 */}

              <Card className="min-w-8  bg-[#1f144a] text-primary-foreground">
                <CardContent
                  className={"font-bold text-sm text-center text-white"}
                >
                  <div className="inline-block align-middle">
                    Security Business Partner
                  </div>
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  N/A
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  N/A
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Knowledge
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              {/* Row 8 */}

              <Card className="min-w-8  bg-[#1f144a] text-primary-foreground">
                <CardContent className={"font-bold text-sm text-center"}>
                  <div className="inline-block align-middle">
                    Senior PKI Manager
                  </div>
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#64C7E9]  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Expert
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight] text-black">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Knowledge
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Knowledge
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F]  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#CBD4D2]  hover:bg-primary hover:text-white">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Knowledge
                </CardContent>
              </Card>

              {/* Row 9 */}

              <Card className="min-w-8  bg-[#1f144a] text-primary-foreground rounded-bl-xl rounded-br-xl">
                <CardContent className={"font-bold text-sm text-center"}>
                  <div className="inline-block align-middle">
                    SMKI RA Manager
                  </div>
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-dccorange hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Supported Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Knowledge
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#9FC33F] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Independent Practitioner
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Knowledge
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Knowledge
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  N/A
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#A1A2A3] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  N/A
                </CardContent>
              </Card>

              <Card className="min-w-8 bg-[#CBD4D2] hover:bg-primary hover:text-white rounded-bl-xl rounded-br-xl">
                <CardContent className="text-xs text-center tracking-tight p-0">
                  Knowledge
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </SidebarInset>
      {/* <main>{children}</main> */}
    </SidebarProvider>
  );
}

export const getStaticProps = async () => {
  const bdata = await supabase
    .from("behavioural_skills")
    .select(
      "id, group, skill_name, behavioural_sub_skills (id, sub_skill_name, level_1_description, level_2_description, level_3_description, level_4_description, level_5_description)"
    );

  const techdata = await supabase
    .from("technical_skills")
    .select(
      "id, skill_name, role_mapping (id, role_id, skill_level), technical_sub_skills (id, sub_skill_name, level_1_description, level_2_description, level_3_description, level_4_description)"
    );

  const rolesdata = await supabase
    .from("roles")
    .select("id, function, team, role_name");

  const responses = await Promise.all([bdata, techdata, rolesdata]);

  console.log(rolesdata);

  return {
    props: {
      behavioural_skills: responses[0].data,
      technical_skills: responses[1].data,
      role_data: responses[2].data,
    },
  };
};
