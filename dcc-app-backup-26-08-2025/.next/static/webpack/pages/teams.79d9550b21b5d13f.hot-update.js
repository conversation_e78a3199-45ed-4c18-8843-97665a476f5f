"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/teams",{

/***/ "(pages-dir-browser)/./pages/teams.js":
/*!************************!*\
  !*** ./pages/teams.js ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: () => (/* binding */ __N_SSG),\n/* harmony export */   \"default\": () => (/* binding */ Teams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @utils/supabaseClient */ \"(pages-dir-browser)/./utils/supabaseClient.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! svg-loaders-react */ \"(pages-dir-browser)/./node_modules/svg-loaders-react/dist/index.js\");\n/* harmony import */ var svg_loaders_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(svg_loaders_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @core/components/ui/sidebar */ \"(pages-dir-browser)/./core/components/ui/sidebar.jsx\");\n/* harmony import */ var _core_components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @core/components/ui/card */ \"(pages-dir-browser)/./core/components/ui/card.jsx\");\n/* harmony import */ var _core_components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @core/components/ui/table */ \"(pages-dir-browser)/./core/components/ui/table.jsx\");\n/* harmony import */ var _core_components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @core/components/ui/select */ \"(pages-dir-browser)/./core/components/ui/select.jsx\");\n/* harmony import */ var _core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @core/components/ui/breadcrumb */ \"(pages-dir-browser)/./core/components/ui/breadcrumb.jsx\");\n/* harmony import */ var _core_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @core/components/ui/separator */ \"(pages-dir-browser)/./core/components/ui/separator.jsx\");\n/* harmony import */ var _core_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @core/components/ui/tabs */ \"(pages-dir-browser)/./core/components/ui/tabs.jsx\");\n/* harmony import */ var _core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @core/components/app-sidebar */ \"(pages-dir-browser)/./core/components/app-sidebar.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Teams(behavioural_skills, technical_skills, role_data) {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedTeam, setSelectedTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Teams.useEffect\": ()=>{\n            // Check for existing session\n            _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession().then({\n                \"Teams.useEffect\": (param)=>{\n                    let { data: { session } } = param;\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if no session\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Teams.useEffect\"]);\n            // Listen for auth state changes\n            const { data: { subscription } } = _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange({\n                \"Teams.useEffect\": (_event, session)=>{\n                    setSession(session);\n                    if (!session) {\n                        // Redirect to index page if session is lost\n                        router.push(\"/\");\n                    }\n                }\n            }[\"Teams.useEffect\"]);\n            return ({\n                \"Teams.useEffect\": ()=>subscription.unsubscribe()\n            })[\"Teams.useEffect\"];\n        }\n    }[\"Teams.useEffect\"], [\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Teams.useEffect\": ()=>{\n            if (session) {\n                getUserProfile();\n            }\n        }\n    }[\"Teams.useEffect\"], [\n        session\n    ]);\n    /* --- DEBUG --- */ console.log(\"user\");\n    console.log(userData);\n    /* --- DEBUG --- */ async function getUserProfile() {\n        try {\n            setLoading(true);\n            const { data: { user } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getUser();\n            if (!user) {\n                throw new Error(\"No user found\");\n            }\n            let { data, error, status } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", user.id).single();\n            if (error && status !== 406) {\n                throw error;\n            }\n            if (data) {\n                setUserData(data);\n            }\n        } catch (error) {\n            console.log(\"Error fetching user profile:\", error.message);\n        // If there's an error fetching profile, we can still show the dashboard\n        // but userData will remain null\n        } finally{\n            setLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_app_sidebar__WEBPACK_IMPORTED_MODULE_12__.AppSidebar, {\n                userData: userData && userData,\n                behavioural_skills: behavioural_skills,\n                technical_skills: technical_skills\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarInset, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 px-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarTrigger, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                orientation: \"vertical\",\n                                className: \"mr-2 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.Breadcrumb, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                            className: \"hidden md:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbLink, {\n                                                href: \"#\",\n                                                children: \"Roles\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbSeparator, {\n                                            className: \"hidden md:block\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_core_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbLink, {\n                                                children: [\n                                                    selectedTeam ? selectedTeam.role_name : \"Select a team\",\n                                                    \" \"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                                lineNumber: 155,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/__data/__dev/Equalital/dev/dcc/dcc-app/dcc-app/pages/teams.js\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n_s(Teams, \"H4a2doBPkiKZrW/uynvbe5N2or0=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Teams;\nvar _c;\n$RefreshReg$(_c, \"Teams\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/teams.js\n"));

/***/ })

});